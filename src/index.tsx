import React from 'react';
import CardExample from './components/CardExample';
import GuidePanel from './components/GuidePanel';
import { AppName } from './types/interface';

function initMainAppHighlightService() {
  console.log("99999999999init======")
  window.guideHighlightService = {
    currentHighlight: null,
    highlightElement: (target, options) => {
      this.clearHighlight();

      const element = this.findElement(target);
      if (!element) {
        console.log('❌ 未找到目标元素: ' + JSON.stringify(target));
        return;
      }

      const rect = element.getBoundingClientRect();
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;
      const padding = (options && options.padding) || 8;
      const borderColor = (options && options.borderColor) || '#1890ff';
      const borderWidth = (options && options.borderWidth) || 2;

      const highlight = document.createElement('div');
      highlight.id = 'guide-highlight-overlay';
      highlight.style.position = 'absolute';
      highlight.style.top = (rect.top + scrollTop - padding) + 'px';
      highlight.style.left = (rect.left + scrollLeft - padding) + 'px';
      highlight.style.width = (rect.width + padding * 2) + 'px';
      highlight.style.height = (rect.height + padding * 2) + 'px';
      highlight.style.border = borderWidth + 'px solid ' + borderColor;
      highlight.style.borderRadius = '4px';
      highlight.style.pointerEvents = 'none';
      highlight.style.zIndex = '999999';
      highlight.style.boxShadow = '0 0 0 9999px rgba(0, 0, 0, 0.3)';
      highlight.style.animation = 'guide-pulse 2s infinite';

      document.body.appendChild(highlight);
      this.currentHighlight = highlight;

      element.scrollIntoView({ behavior: 'smooth', block: 'center' });
      console.log('✅ 高亮元素: ' + (element.textContent || element.tagName).substring(0, 30));
    },

    clearHighlight: () => {
      if (this.currentHighlight) {
        this.currentHighlight.remove();
        this.currentHighlight = null;
        console.log('🧹 高亮已清除');
      }
    },

    findElement: (target) => {
      if (target.selector) {
        return document.querySelector(target.selector);
      }
      if (target.guideId) {
        return document.querySelector('[data-guide-id="' + target.guideId + '"]');
      }
      if (target.text) {
        const elements = Array.from(document.querySelectorAll('button, a, [role="button"]'))
          .filter((el) => {
            return el.textContent && el.textContent.trim().includes(target.text);
          });
        return elements[0] || null;
      }
      return null;
    }
  };
  // 添加样式
  if (!document.getElementById('guide-highlight-styles')) {
    const style = document.createElement('style');
    style.id = 'guide-highlight-styles';
    style.textContent = '@keyframes guide-pulse { 0% { opacity: 1; transform: scale(1); } 50% { opacity: 0.8; transform: scale(1.02); } 100% { opacity: 1; transform: scale(1); } }';
    document.head.appendChild(style);
  }

  console.log('🎯 主应用高亮服务已初始化', window.guideHighlightService);

  // 监听来自微应用的消息
  window.addEventListener('message', (event) => {
    const data = event.data;
    const type = data.type;
    const target = data.target;
    const options = data.options;

    switch (type) {
      case 'GUIDE_HIGHLIGHT':
        console.log('📨 收到高亮请求: ' + JSON.stringify(target));
        window.guideHighlightService.highlightElement(target, options);
        if (event.source) {
          event.source.postMessage({
            type: 'HIGHLIGHT_RESULT',
            data: { success: true, target, options }
          }, '*' as WindowPostMessageOptions);
        }
        break;

      case 'GUIDE_CLEAR_HIGHLIGHT':
        console.log('📨 收到清除高亮请求');
        window.guideHighlightService.clearHighlight();
        if (event.source) {
          event.source.postMessage({
            type: 'CLEAR_HIGHLIGHT_RESULT',
            data: { success: true }
          }, '*' as WindowPostMessageOptions);
        }
        break;

      default:
        if (type && type.indexOf('GUIDE_') === 0) {
          console.log('📨 收到引导消息: ' + type);
        }
    }
  });
}

export default (props: any) => {
  initMainAppHighlightService();

  const components = {
    [AppName.startCard]: <CardExample />,
    [AppName.guidePanel]: <GuidePanel visible {...props} />,
  };

  return components[props?.appName];
}