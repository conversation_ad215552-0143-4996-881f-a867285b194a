import { AppName } from './types/interface';
import { SAMPLE_GUIDE_CONFIG } from './constants/guide';

const config = {
  // alfa widget 本地调试参数
  devCenter: {
  // guidePanel 链接
    props: {
      appName: AppName.guidePanel,
      guide: SAMPLE_GUIDE_CONFIG.guides[0],
      currentStepIndex: 0,
    },
    // // 场景教学卡片 链接
    // props: {
    //   appName: AppName.startCard,
    // },
  },
};

export default config;